import random
import sys
import time
import threading
import json
from queue import Queue
from datetime import datetime, timedelta
from typing import Dict, List, Set

from spain_visa_appointment_date_open import book_appointment
from extension.logger import logger
from extension.session_manager import create_session
from tool import send_dd_msg, area_map, send_wx_post_days
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, publish_redis_msg, save_logs
from user_manager import set_appointment_info, spain_faker_scaning, get_user_info, get_user_centers, spain_user_field
from config import default_centers

# 配置常量
REAL_USER_SCAN_INTERVAL = 3  # 有真实用户地区扫描间隔：3秒
REAL_USER_SUCCESS_PAUSE = 20  # 有真实用户地区成功后暂停：20秒
FAKE_USER_SCAN_INTERVAL = 0.2  # 无真实用户地区扫描间隔
FAKE_USER_SUCCESS_PAUSE = 30 * 60  # 无真实用户地区成功后暂停：30分钟

user_queue = Queue()
user_queue_dict = {}

# 无真实用户地区的VIP和Normal队列存储
fake_user_vip_queues = Queue()  # 存储无真实用户VIP队列
fake_user_normal_queues = Queue() # 存储无真实用户Normal队列


# 扫描状态管理
class ScanStatus:
    def __init__(self):
        self.region_last_scan: Dict[str, float] = {}  # 地区最后扫描时间
        self.region_last_success: Dict[str, float] = {}  # 地区最后成功时间
        self.scanning_regions: Set[str] = set()  # 正在扫描的地区

    def should_scan_region(self, region: str, has_real_users: bool = False) -> bool:
        """判断地区是否应该扫描

        Args:
            region: 地区标识
            has_real_users: 是否有真实用户
        """
        current_time = time.time()

        # 如果正在扫描，跳过
        if region in self.scanning_regions:
            return False

        # 有真实用户的地区：只检查基本扫描间隔（1秒）
        if has_real_users:
            return True

        # 无真实用户的地区：检查成功后的暂停时间
        last_success = self.region_last_success.get(region, 0)
        if current_time - last_success < FAKE_USER_SUCCESS_PAUSE:
            return False
        
        return True

    def start_scanning(self, region: str):
        """开始扫描地区"""
        self.scanning_regions.add(region)
        self.region_last_scan[region] = time.time()
    
    def update_scanning(self, region: str, success: bool):
        """完成扫描地区"""
        if success:
            self.region_last_success[region] = time.time()

    def finish_scanning(self, region: str, success: bool):
        """完成扫描地区"""
        self.scanning_regions.discard(region)
        if success:
            self.region_last_success[region] = time.time()

    def get_next_scan_time(self, region: str, has_real_users: bool = False) -> float:
        """获取下次扫描时间"""
        last_scan = self.region_last_scan.get(region, 0)
        last_success = self.region_last_success.get(region, 0)

        if has_real_users:
            # 有真实用户的地区，使用较短的扫描间隔
            return last_scan + REAL_USER_SCAN_INTERVAL
        else:
            # 无真实用户的地区
            # 如果最近成功过，使用成功暂停时间
            if last_success > last_scan:
                return last_success + FAKE_USER_SUCCESS_PAUSE
            # 否则使用正常扫描间隔
            return last_scan + FAKE_USER_SCAN_INTERVAL


# 全局扫描状态
scan_status_manager = ScanStatus()


def scan_job(user):
    """原始扫描任务"""
    user = get_user_info(user)
    if not user:
        return False, None
    if not user.get("is_login", False):
        return False, None

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    session = create_session(proxy, cookie_dict)

    # 查询预约号
    user["dateVIP"] = True if user.get("acceptVIP", 2) == 1 else False
    # 查询预约号 book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
    flag_book, res_book = book_appointment(user, session)

    visaType = user.get("visaTypeCode")
    area = user.get("centerCode")
    isVIP = user.get("dateVIP", False)
    # 取最新的用户信息
    user = get_user_info(user)
    user["is_login"] = False
    user["dateVIP"] = isVIP
    save_user_2_redis_queue(user)

    if not flag_book:  # 查询放号流程出错了
        logger.error(f"#放号查询失败# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: False, {res_book}")
        return False, None

    success = res_book.get("success")
    if not success:  # 未开放
        logger.info(f"#放号查询结果# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: {success}, {res_book.get('message', '')}")
        info_dict = {"centerCode": area, "dates": [], "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)
        return True, []  # 查询成功但无开放日期
    else:  # 扫号有开放日期，发送通知
        open_days = res_book.get("days", [])
        all_days_str = " | ".join(date_text[5:] for date_text in open_days)
        tip_str = f"西班牙: {area_map.get(area.upper(), area)} {visaType} {'VIP' if isVIP else 'Normal'} 放号。可约日期: {all_days_str}"
        logger.success("#放号查询结果# " + tip_str + " - " + user.get("email"))

        info_dict = {"centerCode": area, "dates": open_days, "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)

        if len(open_days) == 0:  # 如果开放日期不存在，就继续扫号，不暂停
            return True, []

        # 发送redis消息，告知订阅者可以开始预约了
        publish_redis_msg(json.dumps(info_dict, ensure_ascii=False))
        app_field = f"spain-{area}-{visaType}-{isVIP}"
        set_appointment_info(app_field, json.dumps(info_dict, ensure_ascii=False))

        # 发送微信 钉钉消息提醒
        send_dd_msg("#放号查询# " + tip_str, area + visaType + str(isVIP))
        send_wx_post_days("#放号查询# " + tip_str, area + visaType + str(isVIP))

        return True, open_days


def thread_worker_real(center_visaType=None, has_real_users=False):
    """线程工作函数 - 实现队列循环使用和差异化扫描策略

    Args:
        center_visaType: 地区-签证类型-用户类型
        has_real_users: 该地区是否有真实用户
    """
    task_queue = user_queue_dict.get(center_visaType)
    region = center_visaType.replace("-vip", "").replace("-normal", "")

    logger.debug(f"#线程启动# 地区 {region} ({'有真实用户' if has_real_users else '无真实用户'}) 工作线程启动")

    while not task_queue.empty():  # 持续运行
        # 从队列获取用户
        user = task_queue.get()
        # 标记开始扫描
        scan_status_manager.start_scanning(region)

        try:
            # 直接执行扫描任务，每次都使用队列中的新用户
            success, result = scan_job(user)
            if success:  # 扫描成功且有开放日期
                logger.debug(f"#扫描成功# 地区 {region} (有真实用户) 发现开放日期，{REAL_USER_SUCCESS_PAUSE}秒后继续扫描")
                time.sleep(REAL_USER_SUCCESS_PAUSE)  # 成功后间隔20秒
            else:
                # 扫描失败或无开放日期，立即使用下一个用户
                logger.debug(f"#继续扫描# 地区 {region} (有真实用户) 扫描失败，{REAL_USER_SCAN_INTERVAL}秒后使用下一个用户")
                time.sleep(REAL_USER_SCAN_INTERVAL)  # 3秒间隔

        except Exception as e:
            logger.error(f"#线程异常# 地区 {region} 线程工作异常: {e}")
        task_queue.task_done()

    # 更新扫描状态
    scan_status_manager.finish_scanning(region, success)
    logger.debug(f"#线程结束# 地区 {region} 工作线程结束")


def fake_user_thread_worker(center_visaType=None, user_type="vip"):
    """无真实用户地区的专用线程工作函数 - 实现轮次扫描和20分钟暂停

    Args:
        center_visaType: 地区-签证类型
        user_type: 用户类型 ("vip" 或 "normal")
    """
    # region = f"default-faker-queue-{user_type}"
    

    # 根据用户类型选择对应的队列存储
    if user_type == "vip":
        task_queue = fake_user_vip_queues
    else:
        task_queue = fake_user_normal_queues

        # 收集本轮所有用户
    while True:
        if task_queue.empty():
            time.sleep(5)  # 15秒间隔

        user = task_queue.get()
        region = f"{user['centerCode']}-{user['visaTypeCode']}"
        # 标记开始扫描
        scan_status_manager.start_scanning(region)

        try:
            success, result = scan_job(user)
            # 更新扫描状态
            scan_status_manager.update_scanning(region, success)
            time.sleep(FAKE_USER_SCAN_INTERVAL)  # 15秒间隔

        except Exception as e:
            logger.error(f"#无真实用户线程异常# 地区 {region} {user_type.upper()} 线程工作异常: {e}")
            scan_status_manager.update_scanning(region, False)
            time.sleep(FAKE_USER_SCAN_INTERVAL)



def get_regions_info() -> tuple[List[str], Set[str]]:
    """获取所有需要扫描的地区信息，返回(所有地区, 有真实用户的地区)"""
    all_regions = set()
    real_user_regions = set()

    # 获取有真实用户的地区
    spain_users = get_users_with_queue_name(spain_user_field)
    for user in spain_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        real_user_regions.add(center_tag)
        all_regions.add(center_tag)

    # 获取所有虚拟用户地区（确保无真实用户的地区也被扫描）
    all_faker_users = get_users_with_queue_name(spain_faker_scaning)
    for user in all_faker_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        all_regions.add(center_tag)

    # 如果没有任何用户，使用默认地区
    if not all_regions:
        for center in default_centers:
            all_regions.add(f"{center}-Tourism")
            all_regions.add(f"{center}-Business")

    return list(all_regions), real_user_regions, all_faker_users


def start_region_scanning(region: str, has_real_users: bool = False, all_faker_users=[]):
    """启动地区扫描

    Args:
        region: 地区标识 SHANGHAI-Business
        has_real_users: 该地区是否有真实用户
    """
    center_code, visa_type = region.split("-")

    # 获取该地区的虚拟用户
    users = [u for u in all_faker_users if u.get("centerCode") == center_code and u.get("visaTypeCode") == visa_type]

    if not users:
        logger.warning(f"#扫描警告# 地区 {region} 没有可用的虚拟用户")
        return

    # 创建VIP和普通队列
    vip_queue = Queue()
    normal_queue = Queue()

    for user in users:
        if has_real_users:
            if user.get("acceptVIP") == 1:  # VIP用户
                vip_queue.put(user)
            else:  # 普通用户
                normal_queue.put(user)
        else:
            if user.get("acceptVIP") == 1:  # VIP用户
                fake_user_vip_queues.put(user)
            else:  # 普通用户
                fake_user_normal_queues.put(user)

    user_type_label = "有真实用户" if has_real_users else "无真实用户"
    return
    if has_real_users:
        # 有真实用户的地区使用原有逻辑
        if not vip_queue.empty():
            vip_key = f"{region}-vip"
            user_queue_dict[vip_key] = vip_queue
            threading.Thread(target=thread_worker_real, daemon=True, args=(vip_key, has_real_users)).start()
            logger.debug(f"#启动扫描# 地区 {region}-VIP队列 ({user_type_label})，用户数: {vip_queue.qsize()}")

        if not normal_queue.empty():
            normal_key = f"{region}-normal"
            user_queue_dict[normal_key] = normal_queue
            threading.Thread(target=thread_worker_real, daemon=True, args=(normal_key, has_real_users)).start()
            logger.debug(f"#启动扫描# 地区 {region}-Normal队列 ({user_type_label})，用户数: {normal_queue.qsize()}")



def start_scaning():
    """主扫描控制函数"""
    logger.info("#扫号#用户扫号启动")
    logger.info("- 有真实用户的地区：持续扫描，间隔3秒，成功后暂停20秒")
    logger.info("- 无真实用户的地区：轮次扫描，间隔15秒，成功后暂停30分钟，每轮完成后暂停20分钟")
            # 无真实用户的地区使用新的轮次扫描逻辑
    # if not fake_user_vip_queues.empty():
    threading.Thread(target=fake_user_thread_worker, daemon=True, args=("default-faker-queue-vip", "vip")).start()
    # if not fake_user_normal_queues.empty():
    threading.Thread(target=fake_user_thread_worker, daemon=True, args=("default-faker-queue-normal", "normal")).start()
    while True:
        try:
            # 获取所有需要扫描的地区信息
            all_regions, real_user_regions, all_faker_users = get_regions_info()

            if not all_regions:
                logger.warning("#扫描警告# 没有找到任何地区，等待中...")
                time.sleep(60)
                continue

            # 检查每个地区是否需要扫描
            for region in all_regions:
                has_real_users = region in real_user_regions

                if scan_status_manager.should_scan_region(region, has_real_users):
                    region_type = "有真实用户" if has_real_users else "无真实用户"
                    logger.info(f"#开始扫描# 地区 {region} ({region_type})")
                    start_region_scanning(region, has_real_users, all_faker_users)
                else:
                    # 计算下次扫描时间
                    next_scan_time = scan_status_manager.get_next_scan_time(region, has_real_users)
                    remaining_time = next_scan_time - time.time()
                    if remaining_time > 0:
                        region_type = "有真实用户" if has_real_users else "无真实用户"
                        if remaining_time >= 60:
                            logger.debug(f"#等待扫描# 地区 {region} ({region_type}) 还需等待 {remaining_time//60:.1f} 分钟")
                        else:
                            logger.debug(f"#等待扫描# 地区 {region} ({region_type}) 还需等待 {remaining_time:.1f} 秒")

            # 主循环休眠
            time.sleep(5)  # 每5秒检查一次，以支持1秒间隔的扫描

        except Exception as e:
            logger.error(f"#扫描异常# 主循环异常: {e}")
            time.sleep(5)


if __name__ == "__main__":
    # 获取外部参数
    start_scaning()
