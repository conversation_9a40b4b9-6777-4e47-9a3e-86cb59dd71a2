from user_manager import publish_redis_msg, set_appointment_info
from user_manager import redis_client
import json
import time
from extension.logger import logger


def pub_faker_redis_msg():
    while True:
        # 虚拟redis
        msg_json = {
            "centerCode": "SHANGHAI",
            "dates": ["2025-09-01", "2025-09-02", "2025-09-03", "2025-09-04", "2025-09-05", "2025-09-08", "2025-09-09", "2025-09-10", "2025-09-11", "2025-09-12", "2025-09-15"],
            "isVIP": False,
            "visaType": "Tourism",
            "country": "spain",
            "updateTime": time.time(),
        }
        publish_redis_msg(json.dumps(msg_json, ensure_ascii=False))
        app_field = f"spain-{msg_json['centerCode']}-{msg_json['visaType']}-{msg_json['isVIP']}"
        redis_value = redis_client.get(app_field)  # 有redis信息就不覆盖，防止真实信息被覆盖了
        if not redis_value:
            set_appointment_info(app_field, json.dumps(msg_json, ensure_ascii=False))
            logger.info(f"虚拟redis信息: {msg_json}")

        time.sleep(60 * 5)


if __name__ == "__main__":
    pub_faker_redis_msg()

