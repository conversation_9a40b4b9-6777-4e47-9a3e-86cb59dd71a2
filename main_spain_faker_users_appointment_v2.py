import random
import sys
import time
import threading
import json
from queue import Queue

from spain_visa_appointment_date_open import book_appointment
from extension.logger import logger
from extension.session_manager import create_session
from tool import send_dd_msg, area_map, send_wx_post_days
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, publish_redis_msg, save_logs
from user_manager import set_appointment_info, spain_faker_scaning, get_user_info, get_user_centers, spain_user_field
# from config import default_centers
from main_faker_scan_optimeized import start_scaning as start_scaning_optimeized

user_queue = Queue()

# business_queue = Queue()
# tourism_queue = Queue()

user_queue_dict = {}


# 定义一个线程函数，接受参数
# @timmer
def scan_job(user):
    # 登录状态为否 跳过
    if not user:
        return False, None
    user = get_user_info(user)
    if not user:
        return False, None
    if not user.get("is_login", False):
        return False, None

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    session = create_session(proxy, cookie_dict)

    # 查询预约号
    user["dateVIP"] = True if user.get("acceptVIP", 2) == 1 else False
    # user["dateVIP"] = True
    # 查询预约号 book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
    flag_book, res_book = book_appointment(user, session)

    visaType = user.get("visaTypeCode")
    area = user.get("centerCode")
    isVIP = user.get("dateVIP", False)
    # 取最新的用户信息
    user = get_user_info(user)
    user["is_login"] = False
    user["dateVIP"] = isVIP
    save_user_2_redis_queue(user)
    if not flag_book:  # 查询放号流程出错了
        logger.error(f"#放号查询失败# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: False, {res_book}")
        return False, None

    success = res_book.get("success")
    if not success:  # 未开放
        logger.info(f"#放号查询结果# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: {success}, {res_book.get('message')[:]}")
        info_dict = {"centerCode": area, "dates": [], "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)
        return False, None
    else:  # 扫号有开放日期，发送通知到企微
        area = user.get("centerCode")
        # isVIP = user.get("dateVIP", False)  # book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
        open_days = res_book.get("days")
        all_days_str = " | ".join(date_text[5:] for date_text in open_days)
        tip_str = f"西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 放号。可约日期: {all_days_str}"
        logger.success("#放号查询结果# " + tip_str + " - " + user.get("email"))

        info_dict = {"centerCode": area, "dates": open_days, "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)

        if len(open_days) == 0:  # 如果开放日期不存在，就继续扫号，不暂停。
            return False, None
        # 发送redis消息，告知订阅者可以开始预约了
        publish_redis_msg(json.dumps(info_dict, ensure_ascii=False))
        app_field = f"spain-{area}-{visaType}-{isVIP}"
        set_appointment_info(app_field, json.dumps(info_dict, ensure_ascii=False))
        # 发送微信 钉钉消息提醒
        # send_wx_msg("#放号查询# " + tip_str, area + visaType + str(isVIP))
        send_dd_msg("#放号查询# " + tip_str, area + visaType + str(isVIP))
        send_wx_post_days("#放号查询# " + tip_str, area + visaType + str(isVIP))
        # for _ in range(20):
        #     time.sleep(5)
        #     publish_redis_msg(json.dumps(info_dict))
        return True, open_days


def thread_worker(center_visaType=None):
    task_queue = user_queue_dict.get(center_visaType)
    while not task_queue.empty():
        user = task_queue.get()
        pause, _ = scan_job(user)
        if pause:
            time.sleep(18)  # 控制下节奏
        time.sleep(2)
        task_queue.task_done()
        task_queue.put(user)


def start_scaning(thread_count=2):
    logger.info("#扫号#用户扫号....")
    in_scaning_centers = []  # default_centers
    while True:
        user_centers = ["SHANGHAI-Business", "SHANGHAI-Tourism"]  # get_user_centers()
        spain_users = get_users_with_queue_name(spain_user_field)
        for u in spain_users:
            center_tag = u["centerCode"] + "-" + u["visaTypeCode"]
            if center_tag not in in_scaning_centers:
                user_centers.append(center_tag)  # 本次新增的
        user_centers = list(set(user_centers))

        # in_scaning_centers = list(set(user_centers + in_scaning_centers))

        if len(user_centers) == 0:
            time.sleep(5)
            continue
        # 筛选出有真实用户的地区
        # user_centers = ["CHONGQING"]
        all_faker_users = get_users_with_queue_name(spain_faker_scaning)
        for center_tag in user_centers:
            if center_tag in in_scaning_centers:
                continue

            logger.warning(f"#扫号#当前扫描区域: {center_tag}")
            center_code, visaType = center_tag.split("-")
            in_scaning_centers.append(center_tag)
            # 筛选符合的领取和签证类型（tourism、business）
            users = list(filter(lambda x: x.get("centerCode") == center_code and x.get("visaTypeCode") == visaType, all_faker_users))
            if len(users) <= 0:
                time.sleep(5)
                continue

            # user_business = list(filter(lambda x: x.get("visaTypeCode") == "Business", users))
            # user_tourism = list(filter(lambda x: x.get("visaTypeCode") == "Tourism", users))

            business_vip_queue = Queue()
            business_nor_queue = Queue()
            # tourism_nor_queue = Queue()
            # tourism_vip_queue = Queue()

            for user in users:  # user_business
                if user["acceptVIP"] == 2:
                    business_nor_queue.put(user)
                else:
                    business_vip_queue.put(user)

            # for user in user_tourism:
            #     if user["acceptVIP"] == 2:
            #         tourism_nor_queue.put(user)
            #     else:
            #         tourism_vip_queue.put(user)
            user_queue_dict[center_tag + "-vip"] = business_vip_queue
            threading.Thread(target=thread_worker, daemon=True, args=(center_tag + "-vip",)).start()
            user_queue_dict[center_tag + "-normal"] = business_nor_queue
            threading.Thread(target=thread_worker, daemon=True, args=(center_tag + "-normal",)).start()

            # user_queue_dict[center_code + "-Business-vip"] = business_vip_queue
            # threading.Thread(target=thread_worker, daemon=True, args=(center_code + "-Business-vip",)).start()
            # user_queue_dict[center_code + "-Business-normal"] = business_nor_queue
            # threading.Thread(target=thread_worker, daemon=True, args=(center_code + "-Business-normal",)).start()

            # user_queue_dict[center_code + "-Tourism-vip"] = tourism_vip_queue
            # threading.Thread(target=thread_worker, daemon=True, args=(center_code + "-Tourism-vip",)).start()
            # user_queue_dict[center_code + "-Tourism-normal"] = tourism_nor_queue
            # threading.Thread(target=thread_worker, daemon=True, args=(center_code + "-Tourism-normal",)).start()

        time.sleep(5)


if __name__ == "__main__":
    optimeized = False
    if optimeized:
        start_scaning_optimeized()
    else:
        # 获取外部参数
        args = sys.argv[1:]
        thread_count = int(args[0]) if len(args) > 0 else 1
        start_scaning(thread_count)
