from threading import Thread
from queue import Queue
from typing import Callable
import time
import random
import requests
from datetime import datetime
from pypinyin import lazy_pinyin
import threading
from copy import deepcopy
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

from spain_visa_registe import user_registe
from spain_visa_login import user_login
from spain_visa_update_profile import update_user_profile, confirm_email
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, generate_phone_number, generate_random_email, generate_passport_number, pick_verify_code_proxy
from config import headers
from user_manager import save_user_2_redis_queue, del_user_from_redis, get_users_with_queue_name, spain_faker_scaning

DEFAULTWORKER = 4
# 设置重试策略
retries = Retry(
    total=5,  # 总重试次数
    backoff_factor=0.5,  # 退避因子，用于计算重试之间的间隔时间
    status_forcelist=[500, 502, 503, 504, 443],
)  # 需要重试的状态码


# 常见的姓氏和名字列表
common_surnames = [
    "王",
    "李",
    "张",
    "刘",
    "陈",
    "杨",
    "黄",
    "赵",
    "周",
    "吴",
    "徐",
    "孙",
    "朱",
    "马",
    "胡",
    "郭",
    "林",
    "何",
    "高",
    "梁",
    "郑",
    "罗",
    "宋",
    "谢",
    "唐",
    "韩",
    "曹",
    "许",
    "邓",
    "萧",
    "冯",
    "曾",
    "程",
    "蔡",
    "彭",
    "潘",
    "袁",
    "于",
    "董",
    "余",
    "苏",
    "叶",
    "吕",
    "魏",
    "蒋",
    "田",
    "杜",
    "丁",
    "沈",
    "姜",
]

common_given_names = [
    "伟",
    "芳",
    "娜",
    "仟华",
    "敏",
    "静",
    "丽",
    "强",
    "磊",
    "军",
    "洋",
    "勇",
    "艳",
    "杰",
    "娟",
    "涛",
    "明",
    "超",
    "秀兰",
    "霞",
    "平",
    "刚",
    "桂英",
    "英",
    "华",
    "民",
    "强",
    "燕",
    "鹏",
    "辉",
    "玲",
    "波",
    "斌",
    "莉",
    "颖",
    "浩",
    "洁",
    "梅",
    "倩",
    "健",
    "琳",
    "帅",
    "云",
    "丹",
    "峰",
    "坤",
    "俊",
    "红",
    "瑞",
    "婷",
]


# 随机生成中文姓名
def generate_chinese_name():
    surname = random.choice(common_surnames)
    given_name = random.choice(common_given_names)
    chinese_name = surname + given_name
    xing = "".join(lazy_pinyin(surname))
    ming = "".join(lazy_pinyin(given_name))
    return chinese_name, xing.upper(), ming.upper()


def generate_user_birth():
    year = random.randint(1980, 2005)
    month = random.randint(1, 12)
    day = random.randint(1, 28)
    return f"{year}/{month}/{day}"


def generate_travel_date():
    now = datetime.now()
    current_year = now.year
    month = now.month + 1
    if month > 10:
        current_year = current_year + 1
        month = (month + 2) % 12
    day = random.randint(1, 28)
    return f"{current_year}/{month}/{day}"


def generate_passport_date():
    year = random.randint(2020, 2023)
    end_year = year + 10
    month = random.randint(1, 12)
    day = random.randint(2, 28)
    return f"{year}/{month}/{day}", f"{end_year}/{month}/{day-1}"


def generate_appointment_date():
    now = datetime.now()
    year = now.year
    month = now.month + 1
    if month > 11:
        year = year + 1
        month = (month + 1) % 12
    day = random.randint(20, 28)
    return f"{year}/{month}/{day}", f"{year}/{month}/{day-8}"


center_codes = [
    "FUZHOU",
    "GUANGZHOU",
    "BEIJING",
    "HANGZHOU",
    "CHANGSHA",
    "KUNMING",
    "SHANGHAI",
    "SHENYANG",
    "XIAN",
    "JINAN",
    "CHONGQING",
    "SHENZHEN",
    "NANJING",
    "CHENGDU",
]


# 定义一个线程函数，接受参数
def work_registe(center_code="SHANGHAI", visaType=None, is_vip=1, user=None):
    cn_name, surname, first_name = generate_chinese_name()
    p_date_s, p_date_e = generate_passport_date()
    app_date_s, app_date_e = generate_appointment_date()

    time_str = str(int(time.time()))
    if not user:
        user = {
            "email": generate_random_email(),
            "birthday": generate_user_birth(),
            "chnname": cn_name,
            "endDate": "2030/01/01",
            "expiredDT": p_date_e,
            "gender": random.choice(["男", "女"]),
            "name": first_name,
            # "faker_passportNO": generate_passport_number(),
            "passportNO": generate_passport_number(),
            "phone": generate_phone_number(),
            "startDate": "2025/01/01",
            "visaTypeCode": visaType,
            "xing": surname,
            "remark": "测试用户",
            "acceptVIP": int(is_vip),
            "countryCode": "CHN",
            "signLocation": "上海/SHANGHAI",
            "bornplace": "江苏/JIANGSU",
            "passportDate": p_date_s,
            "maritalStatus": "C",
            "travelDate": generate_travel_date(),
            "status": "pending",
            "missionCode": "spain",
            "centerCode": center_code,
            "createTime": time_str,
            "updateTime": time_str,
            "queue_name": spain_faker_scaning,  # "brazil_faker_app_users" "spain_test_users" "spain_date_test" spain_faker_scaning,
        }
        # save_user_2_redis_queue(user)
    user["email"] = generate_random_email(faker=True)
    ua = get_random_user_agent()
    header = deepcopy(headers)
    header["user-agent"] = ua

    session = requests.session()
    session.headers = header
    # user["headers"] = header

    proxy = get_new_proxy()
    proxy_dict = {"http": proxy, "https": proxy}
    session.proxies.update(proxy_dict)
    if user["status"] == "pending":
        flag_resgite, info = user_registe(user, session, False)
        if not flag_resgite:
            logger.error(f"#注册# 注册失败：{user['email']}")
            return flag_resgite, info
        else:
            logger.success(f"#注册# 注册成功:{user['email']}")
            user["status"] = "registe"
            # save_user_2_redis_queue(user)

    flag_login = False
    for _ in range(2):
        flag_login, info_login = user_login(user, session)
        if not flag_login:
            logger.error(f"#注册# 登录失败：{user['email']}")
            proxy = get_new_proxy()
            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)
            continue
        else:
            logger.success(f"#注册# 登录成功:{user['email']}")
            cookie_dict = session.cookies.get_dict()
            user["cookies"] = cookie_dict
            user["proxy"] = proxy
            user["status"] = "login"
            save_user_2_redis_queue(user)
            break

    if not flag_login:
        # del_user_from_redis(user)
        return flag_login, info_login

    flag_profile, res_profile = update_user_profile(user, session)
    if not flag_profile:
        logger.error(f"#注册# 更新失败:{user['email']}")
    else:
        logger.success(f"#注册# 更新成功:{user['email']}")
        user["status"] = "updated"
        save_user_2_redis_queue(user)

    if not flag_profile:
        return flag_profile, res_profile

        # 邮箱双重确认
    if user["status"] == "updated":
        confirm_flag, res_confirm = confirm_email(user, session)
        if not confirm_flag:
            logger.error(f"#注册# 确认邮箱失败:{user['email']}")
        else:
            logger.success(f"#注册# 确认邮箱成功:{user['email']}")
            user["status"] = "update_appointment"
            save_user_2_redis_queue(user)


def worker(center_code, number_user=1, visaTypeOrg="Tourism-1"):
    logger.debug(f"start {center_code}")
    visaType = visaTypeOrg.split("-")[0]
    is_vip = visaTypeOrg.split("-")[-1]
    for _ in range(number_user):  # 注册N个账号
        try:
            work_registe(center_code, visaType, is_vip)
        except Exception as e:
            logger.error(f"失败:{e}")
            continue


def registe_faker_user():
    threads = []
    # 使用 for 循环创建多线程并传入参数
    for visaType in ["Tourism-1", "Tourism-2", "Business-2", "Business-1"] * 1: # ["Tourism-1", "Tourism-2", "Business-2", "Business-1"]
        thread = threading.Thread(target=worker, args=("CHENGDU", 1, visaType), daemon=True)
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    print("end")


if __name__ == "__main__":
    registe_faker_user()
