from queue import Queue
import time
import sys

# from curl_cffi import requests
import requests

import threading
from copy import deepcopy
from urllib3.util.retry import Retry

from spain_visa_registe import user_registe
from spain_visa_login import user_login
from spain_visa_update_profile import update_user_profile, confirm_email
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, generate_phone_number, generate_random_email
from tool import send_dd_msg, get_user_log_msg, send_wx_msg
from tool import update_booking_status, BOOK_STATUS, get_user_head_img
from config import headers, url_host
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_user_field
from user_manager import get_user_info

DEFAULTWORKER = 4
# 设置重试策略
retries = Retry(
    total=5,  # 总重试次数
    backoff_factor=0.5,  # 退避因子，用于计算重试之间的间隔时间
    status_forcelist=[500, 502, 503, 504, 443],
)  # 需要重试的状态码

user_queue = Queue()


# spain_user_field = "spainUserDatas"
def verify_user_head_img(user, session: requests.Session):
    # session = requests.session()
    # session.headers = deepcopy(headers)
    # proxy = get_new_proxy()
    # proxy_dict = {"http": proxy, "https": proxy}
    # session.proxies.update(proxy_dict)
    session.headers.update(
        {
            "requestverificationtoken": "CfDJ8ExjRtuoEK1GjHvsUsGg4ZxrZIiOVePGscdiW18F16aSWboB3-Cf4beYAViMgaNyWYVuaQsHlTZ4fiBmcKh8x5SeBQ_cKbX5WEKzFPLWGqSZtjhbc_vfqhIi6WHEQFZMAGdQCY16YsrRyv4FfrHdv1FaFdclBL4PhDfXHRX-nCC8uWFv20Cj850GF4Ty5fM0Yw",
            "x-requested-with": "XMLHttpRequest",
        }
    )
    avatar_images = user.get("avatar_images")
    if not avatar_images:
        avatar_images = [user.get("avatar_image")]
    url = "https://spain.blscn.cn/CHN/appointment/UploadApplicantPhoto"
    for avatar_image in avatar_images:
        try:
            img_head_bytes = get_user_head_img(user, avatar_image)
            res_upload = session.post(url, files={"file": (f"{user['passportNO']}.png", img_head_bytes, "image/jpeg")}, timeout=5)
            if res_upload.status_code == 200:
                if res_upload.json()["success"]:
                    return True, res_upload.json()
                else:
                    return False, res_upload.json()
            else:
                return False, res_upload.status_code
        except Exception as e:
            proxy = get_new_proxy()
            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)
            logger.error(f"#头像上传# 重试:{e.args[0]}")
    return False, "头像上传失败"


# 定义一个线程函数，接受参数
def work_registe(user):
    # 处理必填字段
    necessary_keys = [
        "birthday",
        "gender",
        "maritalStatus",
        "travelDate",
        "visaTypeCode",
        "expiredDT",
        "passportNO",
        "passportDate",
        "signLocation",
        "name",
        "xing",
        "startDate",
        "endDate",
        "bornplace",
        "centerCode",
    ]
    missing_keys = []
    for key in necessary_keys:
        if not user.get(key, None):
            missing_keys.append(key)

    if len(missing_keys) > 0:
        send_dd_msg(f"#西班牙注册错误# 用户{user.get('chnname')}-{user.get('passportNO')}注册缺少必填字段:{' | '.join(missing_keys)}")
        send_wx_msg(f"#西班牙注册错误# 用户{user.get('chnname')}-{user.get('passportNO')}注册缺少必填字段:{' | '.join(missing_keys)}", user.get("passportNO"))
        return

    session = requests.session()  # requests.Session(impersonate="chrome131")
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session.headers = header

    proxy = get_new_proxy()
    if not proxy:
        return
    proxy_dict = {"http": proxy, "https": proxy}
    session.proxies.update(proxy_dict)

    # 未注册的用户先注册，否则直接登录修改个人信息
    if user.get("status") == "pending":
        user["email"] = generate_random_email()  # 注册时生成虚假的邮箱
        user["phone"] = generate_phone_number()  # 注册时生成虚假的手机号
        user["centerCode"] = user.get("centerCode").upper()
        # user["faker_passportNO"] = generate_passport_number()  # 注册时生成一个虚假的护照号
        user["queue_name"] = spain_user_field  # 用户所处的redis队列名
        user.pop("password", None)

        logger.info(f"开始注册用户: {user.get('chnname')} email:{user['email']}, passport:{user['passportNO']}, area:{user.get('centerCode')}")
        flag_resgite, info = user_registe(user, session)
        if not flag_resgite:
            logger.error(f"#注册# 注册失败：{user.get('chnname')} {user['email']}-{user.get('passportNO')}")
            return flag_resgite, info
        else:
            try:
                log_msg = get_user_log_msg(user)
                send_dd_msg(f"#西班牙注册用户# {log_msg}")
            except:
                pass
            logger.success(f"#注册# 注册成功:{user.get('chnname')} {user['email']}-{user.get('passportNO')},日期:{user.get('startDate')}-{user.get('endDate')}")
            user["status"] = "registe"
            user["proxy"] = proxy
            user_exit = get_user_info(user)  # 判断用户是否被队列删除
            if user_exit:
                save_user_2_redis_queue(user)
                update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "注册成功")
            else:
                logger.info(f"##注册成功## 但用户已被删除:{user.get('chnname')} {user['email']}-{user.get('passportNO')}")

    flag_login = False
    for _ in range(2):
        # user = get_user_info(user)
        flag_login, info_login = user_login(user, session)
        if not flag_login:
            logger.error(f"#注册# 登录失败：{user.get('chnname')} {user.get('passportNO')}")
            proxy = get_new_proxy()
            if not proxy:
                return
            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)
            # save_user_2_redis_queue(user)
            continue
        else:
            logger.success(f"#注册# 登录成功:{user['chnname']} {user['email']} {user.get('passportNO')}")
            cookie_dict = session.cookies.get_dict()
            user["cookies"] = cookie_dict
            user["proxy"] = proxy
            if user["status"] != "update_info":  # 重新编辑不需要修改状态
                user["status"] = "login"
            user["is_login"] = True
            user["updateTime"] = int(time.time())
            save_user_2_redis_queue(user)
            break

    if not flag_login:
        return flag_login, info_login

    if user["status"] == "login" or user["status"] == "update_info":
        # 编辑保存签字申请信息
        flag_profile, res_profile = update_user_profile(user, session)
        if not flag_profile:
            logger.error(f"#注册# 更新失败:{user['chnname']} {user['email']} {user.get('passportNO')}")
        else:
            logger.success(f"#注册# 更新成功:{user['chnname']} {user['email']} {user.get('passportNO')}")
            if user["status"] == "login":  # 初始化注册完成 填写信息
                user["status"] = "updated"
                # update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "信息填写成功")
            elif user["status"] == "update_info":  # 后台修改了信息 重新编辑后直接变成可预约
                user["status"] = "update_appointment"
                log_msg = get_user_log_msg(user)
                update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "信息修改成功")
                head_msg = ""
                if not user.get("image_id", None):
                    try:
                        # 上传头像
                        head_upload, res_upload = verify_user_head_img(user, session)
                        if head_upload:
                            user["image_id"] = res_upload.get("fileId")
                            head_msg = "照片可用:" + str(res_upload)
                            update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "更新信息成功，头像可用")
                        else:
                            # update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "更新信息成功，头像不可用")
                            update_booking_status(user, BOOK_STATUS.AVATAR_FORBIDDEN, "更新信息成功，头像不可用")
                            head_msg = "照片不可用:" + str(res_upload)
                    except Exception as e:
                        logger.error(f"{log_msg}: {e}")
                send_dd_msg(f"#西班牙用户修改信息成功# {log_msg} {head_msg}")
                # send_wx_msg(f"#西班牙用户修改信息成功# {log_msg} {head_msg}")
            save_user_2_redis_queue(user)
        if not flag_profile:
            return flag_profile, res_profile

        # 邮箱双重确认
    if user["status"] == "updated":
        confirm_flag, res_confirm = confirm_email(user, session)
        if not confirm_flag:
            logger.error(f"#注册# 确认邮箱失败:{user['email']}")
        else:
            logger.success(f"#注册# 确认邮箱成功:{user['email']}")
            user["status"] = "update_appointment"
            # 上传头像
            head_upload, res_upload = verify_user_head_img(user, session)
            head_msg = ""
            if head_upload:
                user["image_id"] = res_upload.get("fileId")
                head_msg = "照片可用:" + str(res_upload)
            else:
                head_msg = "照片不可用:" + str(res_upload)
            save_user_2_redis_queue(user)
            try:
                log_msg = get_user_log_msg(user)
                # send_wx_msg(f"#西班牙成功注册# {log_msg}, {head_msg}")
                send_dd_msg(f"#西班牙填表成功# {log_msg}, {head_msg}")
                if head_upload:
                    update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "填表成功，头像可用")
                else:
                    # update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "更新信息成功，头像不可用")
                    update_booking_status(user, BOOK_STATUS.AVATAR_FORBIDDEN, "填表成功，头像不可用")
                # get_user_head_img(user['passportNO'])
            except:
                pass


def worker_thread_registe():
    while not user_queue.empty():
        user = user_queue.get()
        work_registe(user)
        user_queue.task_done()


def registe_spain_user_pipeline(thread_count=5):
    time_sleep_interval = 5
    while True:
        work_start = time.time()
        all_users = get_users_with_queue_name(spain_user_field)
        # 注册新用户 或者 完成已注册用户的信息修改保存
        uncomplete_user = list(filter(lambda x: x.get("status") in ["pending", "registe", "update_info", "login", "updated"], all_users))

        if len(uncomplete_user) <= 0:
            logger.debug(f"队列无用户:{spain_user_field}")
            time.sleep(time_sleep_interval)
            continue
        # print(uncomplete_user)
        for user in uncomplete_user:
            user["queue_name"] = spain_user_field
            # update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "填表成功，头像可用")
            user_queue.put(user)
        threads = []
        # 使用 for 循环创建多线程并传入参数
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_thread_registe, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        t_interval = int(time.time() - work_start)
        logger.info(f"#西班牙用户注册#完成{len(uncomplete_user)}个用户的注册，耗时：{t_interval} s")
        time.sleep(time_sleep_interval)

if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else 1
    logger.info("开始监控新用户，并注册")
    registe_spain_user_pipeline(thread_count)
    # users = get_users_with_queue_name("spainUserDatas")
    # work_registe("shanghai", users[0])
