#!/bin/bash

echo "正在停止所有faker应用..."

# 定义要停止的脚本列表
scripts=(
    "main_spain_faker_users_login.py"
    "main_spain_faker_users_appointment_v2.py"
    "main_spain_slot_cancel.py"
    "main_spain_faker_redis_pub.py"
)

# 停止每个脚本的进程
for script in "${scripts[@]}"; do
    echo "检查进程: $script"

    # 获取所有匹配的PID（可能有多个）
    pids=$(pgrep -f "$script")

    if [ -z "$pids" ]; then
        echo "  进程未找到: $script"
    else
        echo "  找到进程PID: $pids"
        for pid in $pids; do
            echo "  正在关闭进程: $script (PID: $pid)"
            kill "$pid"

            # 等待进程结束
            sleep 1

            # 检查进程是否还在运行
            if kill -0 "$pid" 2>/dev/null; then
                echo "  进程 $pid 未响应，强制关闭..."
                kill -9 "$pid"
            else
                echo "  进程 $pid 已成功关闭"
            fi
        done
    fi
    echo ""
done

# 等待一下确保所有进程都已关闭
sleep 2

# 最终检查
echo "最终检查剩余进程:"
remaining=$(ps aux | grep python3 | grep -E "(main_spain_faker_users_login|main_spain_faker_users_appointment_v2|main_spain_slot_cancel)" | grep -v grep)

if [ -z "$remaining" ]; then
    echo "所有faker应用已成功停止！"
else
    echo "警告: 仍有进程在运行:"
    echo "$remaining"
fi

pid=$(pgrep -f main_spain_faker_redis_pub.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_faker_redis_pub"
else
    echo "关闭进程: main_spain_faker_redis_pub,PID为: $pid"
    kill "$pid"
fi

