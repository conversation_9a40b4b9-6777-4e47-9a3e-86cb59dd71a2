# from curl_cffi import requests as curl
import requests, time
from user_manager import get_email_otp, move_user_2_queue, del_user_from_redis, save_user_2_redis_queue
from extension.logger import logger
from user_manager import spain_delete_users, spain_user_field, spain_faker_scaning, spain_error_users
from bypass_login_page import bypass_login_page, extract_form_data_from_html
from bypass_verify_code import bypass_verify_code_pipeline, bypass_verify_code_func
from tool import get_new_proxy, extract_alert_msg
from config import headers, user_pwd, user_pwd_2


# user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

url_host = "https://spain.blscn.cn"
url_home = "https://spain.blscn.cn/CHN/account/login"
url_login_submit = "https://spain.blscn.cn/CHN/account/loginsubmit"
url_pwd_change = "https://spain.blscn.cn/CHN/account/ChangeUserPassword"

session_d = requests.session()


def user_login(account, session=session_d):
    try:
        password = account.get("password")
        user_name = account.get("email")
        # status = account.get("status")
        # logger.debug(f"正在登录{user_name}...")
        res_home = session.get(url_home, verify=False, timeout=15)
        if res_home.status_code != 200:
            logger.error(f"#登录# 首页请求错误。{res_home.status_code}: {res_home.text[-160:]}")
            return False, res_home

        res_home_html = res_home.text
        flag_parse, post_data, uri_post_form = bypass_login_page(res_home_html, user_name)
        if not flag_parse:
            logger.error("#登录# 邮箱页面解析失败")
            return False, post_data

        # 发送邮箱输入页面，跳转到验证码，密码页面
        res_post_login = session.post(url_host + uri_post_form, data=post_data, timeout=15, verify=False)
        if res_post_login.status_code != 200 or "/CHN/newcaptcha/logincaptcha".upper() not in res_post_login.url.upper():
            msg = extract_alert_msg(res_post_login.text)
            logger.error(f"#登录# 登录失败:{user_name} {account.get('chnname')}, {msg}")
            if "Given Email does not exist".upper() in msg.upper():
                if account["queue_name"] == spain_user_field:
                    move_user_2_queue(account, spain_delete_users)
                elif account["queue_name"] in [spain_faker_scaning, spain_error_users]:
                    return False, "deleted"
            return False, res_post_login.text

        if not password:  # 刚完成注册还没有修改密码，需要从邮箱获取密码
            password = get_email_otp(user_name)
            if password:
                # account = get_user_info(account)
                account["password"] = password
                save_user_2_redis_queue(account)  # TODO
            else:
                logger.error(f"#登录#从redis读取邮箱确认码错误:{user_name}, {account.get('centerCode')}, {account.get('queue_name')}")
                return False, 502
        if not password:
            logger.error("#登录# 从redis读取邮箱确认码错误")
            return False, -1

        html_doc = res_post_login.text
        res_submit = None
        for _ in range(3):
            flag_captcha, form_dict, answer_number, uri_captcha_submit = bypass_verify_code_func(html_doc, method_name="VerifyLogin", pasword=password)
            if not flag_captcha:
                return False, "#登录# 密码页解析错误"
            res_submit = session.post(url_host + uri_captcha_submit, data=form_dict, timeout=15)
            # time_start = time.time()
            # logger.info(f"time: {time_start}")
            # while True:
            #     time.sleep(60)
            #     try:
            #         session2 = requests.session()
            #         proxy = get_new_proxy()
            #         proxy_dict = {"http": proxy, "https": proxy}
            #         session2.proxies.update(proxy_dict)
            #         session2.headers.update(session.headers)
            #         res_submit2 = session2.post(url_host + uri_captcha_submit, data=form_dict, timeout=15)
            #         if res_submit2.status_code in [500, 501, 502, 503, 504]:
            #             continue
            #     except:
            #         continue
            #     if "chn/home/<USER>".upper() in res_submit2.url.upper():
            #         logger.info("登录信息有效")
            #     else:
            #         logger.info(f"登录信息失效: {time.time()-time_start}")
            #         print("11")
            if res_submit.status_code != 200:
                continue
                # return False, "验证码失败"
            # 重试验证码解析
            if "/CHN/newcaptcha/logincaptcha".upper() in res_submit.url.upper():
                err_msg = extract_alert_msg(res_submit.text)
                logger.warning(f"#登录# 图片验证码校验:{account.get('chnname')} {user_name} {err_msg}")
                if err_msg.upper() == "EXCEPTION":
                    password = "Kq123456@"
                    account["password"] = password
                html_doc = res_submit.text
                continue
            else:
                break

        # 修改默认密码
        if "/CHN/account/changepassword?alert=True".upper() in res_submit.url.upper():
            update_pwd_form = extract_form_data_from_html(res_submit.text)
            # 默认密码修改成 Kq23456@ / #
            new_password = user_pwd_2 if password == user_pwd else user_pwd
            update_pwd_form["CurrentPassword"] = password
            update_pwd_form["NewPassword"] = new_password
            update_pwd_form["ConfirmPassword"] = new_password

            for _ in range(3):
                res_pwd = session.post(url_pwd_change, data=update_pwd_form, verify=False, timeout=15)
                if res_pwd.status_code in [500, 502, 503, 504]:
                    continue
                if res_pwd.status_code not in [200, 302] or not res_pwd.json().get("success"):
                    logger.error(f"#登录# 设置新密码失败。{user_name}:{res_pwd.status_code}: {res_pwd.json()}")
                    return False, res_pwd
                else:
                    account["password"] = new_password
                    save_user_2_redis_queue(account)
                    logger.info(f"#登录# {user_name}:修改默认密码成功")
                    break
        elif "chn/home/<USER>".upper() in res_submit.url.upper():
            logger.debug(f"#登录# 跳转首页{res_submit.url}")
        else:
            pass

        account["dest"] = "0cf9da11-cb76-4566-81e5-8628d5488e3c"  # 目的地
        return True, account

    except Exception as e:
        logger.error(f"#登录# 出错：{account['email']}, error: {e.args[0]}")
        return False, e


if __name__ == "__main__":
    proxy = get_new_proxy()
    proxy_dict = {"http": proxy, "https": proxy}
    user1 = {"email": "<EMAIL>", "password": "Kq123456@"}

    user = user1
    session = requests.session()
    session.headers = headers
    session.proxies = proxy_dict
    user_login(user, session)
    # 429 换IP


def captcha_test(url, data):
    time_start = time.time()
    logger.info(f"time: {time_start}")
    while True:
        time.sleep(60)
        try:
            session2 = requests.session()
            proxy = get_new_proxy()
            proxy_dict = {"http": proxy, "https": proxy}
            session2.proxies.update(proxy_dict)
            session2.headers.update(session.headers)
            res_submit2 = session2.post(url, data=data, timeout=15)
            if res_submit2.status_code in [500, 501, 502, 503, 504]:
                continue
        except:
            continue
        if "chn/home/<USER>".upper() in res_submit2.url.upper():
            logger.info("登录信息有效")
        else:
            logger.info(f"登录信息失效: {time.time()-time_start}")
            print("11")
