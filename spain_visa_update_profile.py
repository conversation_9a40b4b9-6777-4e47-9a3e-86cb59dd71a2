import requests
import re
import random
from user_manager import get_email_otp, get_users_with_queue_name, spain_user_field, move_user_2_queue, spain_error_users
from extension.logger import logger
from curl_cffi import requests as curl_requests
from bypass_login_page import extract_editId_locationId_visaTypeId, extract_form_data_from_html, extract_manager_uri
from tool import wx_hook_urls, send_dd_msg, area_map, extract_alert_msg
from config import genderData, journeyPurposeData, maritalStatusData
from user_manager import save_user_2_redis_queue
from tool import update_booking_status, BOOK_STATUS

"""
Place Of Birth*
Gender*
Marital Status*
Travel Date*
Purpose Of Journey*
HomeAddressCountryId
"""
url_profile_submit = "https://spain.blscn.cn/CHN/appointmentData/ManageApplicant"
url_host = "https://spain.blscn.cn"


def filter_from_array_with_name(source_arr, name):
    item = list(filter(lambda x: x.get("Name") == name, source_arr))
    return item


locationData = [
    {"Id": "9c400f4a-4458-45b9-b8c0-657c02e54607", "Name": "Changsha", "Code": "CHANGSHA", "VisaTypeIds": "", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "e7f4ae3a-0c02-41ce-a7bb-89527197af61", "Name": "Kunming", "Code": "KUNMING", "VisaTypeIds": "", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "8d780684-1524-4bda-b138-7c71a8591944", "Name": "Beijing", "Code": "BEIJING", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "6f4eca74-7a15-480a-8401-a58146cc2d97", "Name": "Wuhan", "Code": "WUHAN", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "bb164660-e355-48eb-93fe-df68664caf14", "Name": "Hangzhou", "Code": "HANGZHOU", "VisaTypeIds": "", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
    {"Id": "4385a0c3-0332-430d-a8aa-1e45a6affd9a", "Name": "Guangzhou", "Code": "GUANGZHOU", "VisaTypeIds": "", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "fa974c17-c38a-4481-89bd-15332ee9a57b", "Name": "Fuzhou", "Code": "FUZHOU", "VisaTypeIds": "", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "1805e27d-ddd6-4148-af8e-3808927748de", "Name": "Shenzhen", "Code": "SHENZHEN", "VisaTypeIds": "", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "06dca747-d1a6-4c05-a4ba-fa3239079e9b", "Name": "Chengdu", "Code": "CHENGDU", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "fd1919e9-da2a-4cc7-86b1-b8937b8594ca", "Name": "Xi'an", "Code": "XIAN", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "41f1bbfc-0535-4984-aa20-cd37ee33a6bf", "Name": "Shanghai", "Code": "SHANGHAI", "VisaTypeIds": "", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
    {"Id": "1e413a56-d561-42e1-b989-4687bee7f661", "Name": "Chongqing", "Code": "CHONGQING", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "8321d24a-d6bc-433d-a4a8-8652f49bbd5e", "Name": "Shenyang", "Code": "SHENYANG", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "baa2c077-c4ee-4d02-884a-c668035c6ec5", "Name": "Jinan", "Code": "JINAN", "VisaTypeIds": "", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "442fb5dd-ddca-4a11-a16d-1110b923f3c1", "Name": "Nanjing", "Code": "NANJING", "VisaTypeIds": "", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
]


def update_user_profile(user_info, session):
    # user_id = user_info.get("userId")
    try:
        # 进入编辑信息页面
        book_appointment_url = url_host + "/CHN/appointmentdata/myappointments"
        res_page_applicant = session.get(book_appointment_url, verify=False, timeout=15, allow_redirects=False)
        if res_page_applicant.status_code not in [200, 302]:
            logger.error(f"{user_info['email']}:{user_info.get('passportNO')},首页进入失败：{res_page_applicant.text[:]}")
            return False, res_page_applicant

        editId, visaTypeId, locationId = extract_editId_locationId_visaTypeId(res_page_applicant.text)
        if editId:
            edit_url = url_host + "/CHN/appointmentdata/IsAppointmentExist?id=" + editId
            if not visaTypeId:
                visaTypeId = "099a0161-b428-4a10-bb1e-639b7dee4fa0"  # shengen_visa
            # if not locationId:
            user_location = user_info.get("centerCode", "SHANGHAI").upper()
            loation_item = list(filter(lambda x: x.get("Code").upper() == user_location, locationData))
            locationId = loation_item[0].get("Id")
        else:
            return False, res_page_applicant
        # # 预算预约的确， 签证类型  BEIJING shengen_visa
        res_applicant_exist = session.get(edit_url, verify=False, timeout=15, allow_redirects=False)
        # session.headers.update({"referer": book_appointment_url})
        if res_applicant_exist.status_code != 200:
            logger.error(f"{user_info['email']}:{user_info.get('passportNO')}, 编辑页面请求失败：{res_applicant_exist.status_code}:{res_applicant_exist.text}")
            return False, res_applicant_exist

        manager_uri = extract_manager_uri(res_page_applicant.text)
        manager_url = url_host + manager_uri + f"{editId}&visaTypeId={visaTypeId}&locationId={locationId}"
        # 获取编辑信息详情
        res_profile_info = session.get(manager_url, verify=False, timeout=15, allow_redirects=False)
        if res_profile_info.status_code != 200:
            logger.error(f"{user_info['email']}:{user_info.get('passportNO')}, 编辑页面请求失败：{res_profile_info.status_code}:{res_profile_info.text}")
            return False, res_profile_info

        user_apply_info = extract_form_data_from_html(res_profile_info.text)
        # 出生日期
        user_apply_info["DateOfBirth"] = user_info.get("birthday").replace("/", "-")
        user_apply_info["ExpiryDate"] = user_info.get("expiredDT").replace("/", "-")  # user_apply_info["ExpiryDate"][:10] # user_info.get("passportDate").replace("/", "-")
        user_apply_info["IssueDate"] = user_info.get("passportDate").replace("/", "-")  # user_apply_info["IssueDate"][:10] # user_info.get("expiredDT").replace("/", "-")
        # apply_no = "241120" + "-" + "".join(random.choices("0123456789", k=6))
        # if not user_info.get("applyNO", None):
        #     user_info["applyNO"] = apply_no
        # user_apply_info["AppointmentProtocolNo"] = user_info.get("applyNO").replace(" ", "")
        # 性别
        user_sex = user_info.get("gender", "男")
        user_apply_info["GenderId"] = genderData[user_sex]
        user_info["GenderId"] = user_apply_info["GenderId"]
        # 婚姻状况
        item_marital = maritalStatusData[user_info.get("maritalStatus", "C").upper()]
        user_apply_info["MaritalStatusId"] = item_marital.get("Id")
        user_info["MaritalStatusId"] = user_apply_info["MaritalStatusId"]
        # 前往时间
        user_apply_info["TravelDate"] = user_info.get("travelDate").replace("/", "-")[:10]

        # # 豁免理由：
        # # 0: 过去59个月签证打印件 1：法律原因 2：无原因
        user_apply_info["PreviousFingerPrintStatus"] = 2
        # # 申请人详细信息
        # # 0：公司机构 1：酒店零时住所 2：邀请人
        user_apply_info["BlsInvitingAuthority"] = 1
        # # 签证目的
        purpose = user_info.get("visaTypeCode", "Tourism")
        default_purpose = {"Id": "82a413f6-0e05-4ac0-8224-61432e8dfa44", "Name": "Tourism", "Code": "10"}
        user_apply_info["PurposeOfJourneyId"] = journeyPurposeData.get(purpose, default_purpose).get("Id")
        user_info["PurposeOfJourneyId"] = user_apply_info["PurposeOfJourneyId"]
        # 地址
        user_apply_info["HomeAddressCountryId"] = user_info.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72")
        user_apply_info["CountryOfBirthId"] = user_info.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72")
        user_apply_info["NationalityAtBirthId"] = user_info.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72")
        user_apply_info["NationalityId"] = user_info.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72")
        user_apply_info["PlaceOfBirth"] = user_info.get("bornplace", "上海/SHANGHAI").split("/")[-1]
        user_apply_info["MemberStateDestinationId"] = user_info.get("dest", "0cf9da11-cb76-4566-81e5-8628d5488e3c4")  # 国家ID
        # user_apply_info["MemberStateSecondDestinationId"] = user_info.get("dest", "3840e794-70c8-49d3-a52a-c531007440ee")
        # user_apply_info["MemberStateFirstEntryId"] = user_info.get("dest", "3840e794-70c8-49d3-a52a-c531007440ee")
        session.headers.update({"content-type": "application/x-www-form-urlencoded; charset=UTF-8"})
        for _ in range(3):
            res_submit = session.post(url_profile_submit, data=user_apply_info, verify=False, timeout=15, allow_redirects=False)
            session.headers.pop("content-type", None)
            if res_submit.status_code == 200:
                if res_submit.json()["success"]:
                    logger.info(f"{user_info['email']}:{user_info.get('passportNO')}, 更新注册信息成功")
                    return True, res_submit
                else:  # 户护照号重复了
                    log_string = f"#西班牙注册# 更新信息错误 {user_info.get('chnname')} {area_map.get(user_info.get('centerCode'))}  {user_info.get('visaTypeCode')} {user_info.get('passportNO')} {user_info.get('email')} , 原因: {res_submit.json().get('error')}"
                    logger.error(log_string)
                    # 真实用户
                    if user_info.get("queue_name") == spain_user_field:
                        url = random.choice(wx_hook_urls)
                        requests.post(url, json={"msgtype": "text", "text": {"content": log_string}}, timeout=5)
                        send_dd_msg(log_string)
                        user_info["status"] = "upadte_error"  # 无效的用户
                        move_user_2_queue(user_info, spain_error_users)  # 移动到注销队列
                        err_reason = res_submit.json().get("error")
                        if "Passport number of Applicant is duplicating" in err_reason:
                            err_reason = "填表时护照被占用，解绑原有填表中的护照信息"
                        elif "Active Appointments are available for the Applicant Passport" in err_reason:
                            err_reason = "该护照已有预约信息:" + "Active Appointments are available for the Applicant Passport"
                        elif "An appointment is already booked for this member" in err_reason:
                            err_reason = "该护照已有预约信息:" + "An appointment is already booked for this member"
                        update_booking_status(user_info, BOOK_STATUS.REGISTE_ERROR, err_reason)
                    return False, res_submit
            elif res_submit.status_code == 400:
                err_reason = extract_alert_msg(res_profile_info.text)
                if "Passport number of Applicant is duplicating" in err_reason:
                    err_reason = "填表时护照被占用，解绑原有填表中的护照信息"
                elif "Active Appointments are available for the Applicant Passport" in err_reason:
                    err_reason = "该护照已有预约信息:" + "Active Appointments are available for the Applicant Passport"
                elif "An appointment is already booked for this member" in err_reason:
                    err_reason = "该护照已有预约信息:" + "An appointment is already booked for this member"
                # if err_reason:
                #     update_booking_status(user_info, BOOK_STATUS.REGISTE_ERROR, err_reason)
                #     send_dd_msg(log_string)
                #     user_info["status"] = "upadte_error"  # 无效的用户
                #     move_user_2_queue(user_info, spain_error_users)  # 移动到注销队列
                logger.error(f" {user_info.get('chnname')} {user_info['email']}更新注册信息失败:{res_submit.text} {err_reason}")
                return False, err_reason
            else:
                logger.error(f" {user_info.get('chnname')} {user_info['email']}更新注册信息失败:{res_submit.status_code} {res_submit.text}")
                return False, res_submit
        return False, None
    except Exception as e:
        logger.error(f"{user_info.get('chnname')}  {user_info['email']}:{user_info.get('passportNO')}, 更新注册信息失败：{e.args[0]}")
        return False, None


def confirm_email(user_info, session):
    try:
        user_email = user_info["email"]
        user_passportNO = user_info.get("passportNO")
        log_user = f"#确认邮箱# {user_info.get('chnname')} {user_email}:{user_passportNO}"
        # book_appointment_url = url_host + "/CHN/appointment/newappointment"  #
        # res_page_applicant = session.get(book_appointment_url, verify=False, timeout=15, allow_redirects=True)
        # if res_page_applicant.status_code != 200:
        #     if "/CHN/appointment/newappointment".upper() in res_page_applicant.url.upper():
        #         pass
        #         return True, res_page_applicant.status_code
        #     logger.error(f"{log_user}, 首页进入失败：{res_page_applicant.text[:]}")
        #     return False, res_page_applicant

        # logger.debug("接受协议...")
        accept_url = url_host + "/CHN/appointment/biometricaccepted"
        res_accept = session.get(accept_url, verify=False, timeout=15, allow_redirects=True)
        if res_accept.status_code != 200 or not res_accept.json().get("success"):
            logger.error(f"#双重认证# {log_user}, biometricaccepted失败: {res_accept.text[:]}")
            return False, res_accept

        send_email_url = url_host + "/CHN/appointment/DataProtectionEmailSent"
        res_email_send = session.get(send_email_url, verify=False, timeout=15, allow_redirects=True)
        if res_email_send.status_code != 200:
            logger.error(f"#双重认证# {log_user}, res_email_send Error{res_email_send.text[:]}")
            return False, res_accept

        # TODO get comfirm url
        # 从redis获取邮箱的确认链接，确认协议
        if user_info.get("confirm_url"):
            confirm_url = user_info["confirm_url"]
        else:
            confirm_url = get_email_otp(user_email)
            user_info["confirm_url"] = confirm_url
            save_user_2_redis_queue(user_info)
        for _ in range(10):
            # 这里不能用session请求，会报错 449
            res_confirm = curl_requests.get(confirm_url, impersonate="safari17_0", verify=False, allow_redirects=False)
            if res_confirm.status_code not in [200, 302]:
                logger.error(f"#双重认证# {log_user}, res_email_send Error: {res_confirm.text[:]}")
                return False, res_confirm

            link_html = res_confirm.text
            pattern = r'https://[^\s"]+'  # 匹配 https:// 开头，直到空格或双引号结束
            match = re.search(pattern, link_html)
            if match:
                forward_url = match.group(0)
                res_forward = session.get(forward_url, verify=False, allow_redirects=True)
                if res_forward.status_code == 200:
                    msg = extract_alert_msg(res_forward.text)
                    logger.success(f"#双重认证# {log_user} 邮箱确认：{msg}")
                    user_info.pop("confirm_url", None)
                    return True, True
        return False, False
    except Exception as e:
        logger.error(f"#双重认证# {log_user} Error confirm_email: {e}", exc_info=True)
        return False, e


if __name__ == "__main__":
    users = get_users_with_queue_name()
