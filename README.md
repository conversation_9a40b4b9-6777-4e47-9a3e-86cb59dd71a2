# SpainVisa
# 西班牙签证

## TODO 2025-08-27
1. web重新预约有个bug。redis没用户。

## TODO 2025-08-01
① 改成多人约一天，做标记（一个扫到了等待另一个扫号，都扫到号了才继续预约.） -->优先级2
② 提高预约成功率：图片-提前判断（状态-可预约）、页面提供上传多个图片的入口，预约的时候来回切换、403问题--自动优化图片 -->优先级2
③ 提高预约效率--快速预约成功 -->优先级1
④ 邮箱能改成 微软的吗？ 以后邮箱要给客户的 -->优先级3
⑤ 付款失败，加回去后自动加上，而不是客户自己去记  -->优先级1
⑥ 消息提醒优化：注册成功--填表成功--可预约--预约成功待付款--预约信下载 -->优先级1
     失败消息提醒： 
     1. 图片尺寸不对（缩小尺寸重新上传）
     2. 护照被占用，可注销原有绑定账号
     3. 填表时-护照被占用，解绑原有填表中-护照信息
     4. 该护照已有预约信息
     5. 其他错误：展示系统返回的英文解释

⑦. 西班牙 类型默认 旅游  -->优先级1
⑧. 国家 城市 类型红色高亮 每一步都显示  -->优先级1
⑨ 付款倒计时显示 9分30秒  -->优先级1

 <ul><li>The value '2024-03-00' is not valid for PassportIssueDate.</li><li>The value '2024-03-00' is not valid for ServerPassportIssueDate.</li></ul>
Passport of User should have at least 180 days validity

#### 开机启动
``` sudo systemctl enable crond ```

## 2025-07-07 TODO LIST
1. 状态变化调用接口
2. 注册完成检查用户是否存在，不存在就注销
3. 随机用户密码，根据用户信息设置

## 2025-07-11
1. 收到放号信息持续预约
2. 添加虚拟用户验证真实用户头像是否可用

## 2025-07-20
1. 头像状态同步后台
2. 扫号策略修改按照真实用户分布来确定


