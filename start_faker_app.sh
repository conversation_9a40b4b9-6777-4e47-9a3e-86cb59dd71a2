#!/bin/bash

# 获取当前目录路径（修复语法错误：变量赋值不能有空格）
dir_path=$(pwd)
echo "当前工作目录: $dir_path"
cd /root/visa/SpainVisa

# 确保日志目录存在
mkdir -p logs_today

# 停止现有的faker应用
echo "停止现有的faker应用..."
if [ -f "stop_faker_app.sh" ]; then
    sh stop_faker_app.sh
else
    echo "警告: stop_faker_app.sh 文件不存在"
fi

# 激活conda环境
echo "激活conda环境..."
if [ -f "/root/visa/conda_env/bin/activate" ]; then
    source /root/visa/conda_env/bin/activate
    echo "conda环境已激活"
else
    echo "警告: conda环境路径不存在，使用系统Python"
fi

# 检查Python脚本是否存在并启动
echo "启动脚本 main_spain_faker_users_login.py"
if [ -f "main_spain_faker_users_login.py" ]; then
    nohup python3 main_spain_faker_users_login.py 35 > logs_today/faker_login.log 2>&1 &
    echo "main_spain_faker_users_login.py 已启动，PID: $!"
else
    echo "错误: main_spain_faker_users_login.py 文件不存在"
fi

echo "启动脚本 main_spain_faker_users_appointment_v2.py"
if [ -f "main_spain_faker_users_appointment_v2.py" ]; then
    nohup python3 main_spain_faker_users_appointment_v2.py > logs_today/faker_scaning.log 2>&1 &
    echo "main_spain_faker_users_appointment_v2.py 已启动，PID: $!"
else
    echo "错误: main_spain_faker_users_appointment_v2.py 文件不存在"
fi

echo "启动脚本 main_spain_slot_cancel.py"
if [ -f "main_spain_slot_cancel.py" ]; then
    nohup python3 main_spain_slot_cancel.py 2 > logs_today/spain_user_slot_cancel.log 2>&1 &
    echo "main_spain_slot_cancel.py 已启动，PID: $!"
else
    echo "错误: main_spain_slot_cancel.py 文件不存在"
fi

nohup python3 main_spain_faker_redis_pub.py > logs_today/spain_faker_redis.log 2>&1 &


# 等待一下让进程启动
# sleep 1

# # 显示启动的进程
# echo ""
# echo "当前运行的Python进程:"
# ps aux | grep python3 | grep -E "(main_spain_faker_users_login|main_spain_faker_users_appointment_v2|main_spain_slot_cancel)" | grep -v grep

echo ""
echo "所有faker应用已启动完成！"
echo "日志文件位置:"
echo "  - 登录日志: logs_today/faker_login.log"
echo "  - 扫描日志: logs_today/faker_scaning.log"
echo "  - 取消日志: logs_today/spain_user_slot_cancel.log"
# nohup python3 main_spain_slot_cancel.py 2 > logs_today/spain_user_slot_cancel.log 2>&1 &

